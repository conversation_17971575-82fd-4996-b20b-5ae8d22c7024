/// Background Token Manager - GymKod Pro Mobile
///
/// Bu service background'da token yönetimi yapar.
/// Uygulama background'dayken token'ların geçerliliğini kontrol eder ve gerekirse yeniler.
/// Referans: Angular frontend'deki background token management pattern
library;

import 'dart:async';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'services.dart';

/// Background Token Manager
/// Background'da token yönetimi yapar
class BackgroundTokenManager {
  static final BackgroundTokenManager _instance = BackgroundTokenManager._internal();
  factory BackgroundTokenManager() => _instance;
  BackgroundTokenManager._internal();

  // Services
  final StorageService _storageService = StorageService();
  final JwtService _jwtService = JwtService();

  // Timer management
  Timer? _backgroundTimer;
  DateTime? _lastTokenCheck;
  DateTime? _lastSuccessfulRefresh;
  bool _isBackgroundActive = false;
  bool _isPerformingCheck = false;

  // Statistics
  int _backgroundCheckCount = 0;
  int _successfulRefreshCount = 0;
  int _failedRefreshCount = 0;

  /// Background token management başlat
  void startBackgroundManagement() {
    if (_isBackgroundActive) {
      LoggingService.authLog('Background token management already active');
      return;
    }

    try {
      _isBackgroundActive = true;
      _backgroundCheckCount = 0;
      _successfulRefreshCount = 0;
      _failedRefreshCount = 0;

      _backgroundTimer = Timer.periodic(
        AppConstants.backgroundTokenCheckInterval,
        (_) => _performBackgroundTokenCheck(),
      );

      LoggingService.authLog('Background token management started',
        details: 'Check interval: ${AppConstants.backgroundTokenCheckInterval.inMinutes} minutes');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'BackgroundTokenManager startBackgroundManagement');
    }
  }

  /// Background'da token kontrolü (10K+ kullanıcı için optimize edildi)
  Future<void> _performBackgroundTokenCheck() async {
    if (_isPerformingCheck || !_isBackgroundActive) {
      LoggingService.authLog('Background check skipped - already performing or inactive');
      return;
    }

    try {
      _isPerformingCheck = true;
      _backgroundCheckCount++;
      final now = DateTime.now();

      LoggingService.authLog('Background token check started',
        details: 'Check #$_backgroundCheckCount');

      // Son kontrol 2 dakikadan eskiyse kontrol et (scalability için optimize edildi)
      if (_lastTokenCheck != null &&
          now.difference(_lastTokenCheck!).inMinutes < 2) {
        LoggingService.authLog('Background check skipped - too recent',
          details: 'Last check: ${now.difference(_lastTokenCheck!).inMinutes} minutes ago');
        return;
      }

      _lastTokenCheck = now;

      // Token'ları al
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) {
        LoggingService.authLog('Background check: No access token');
        return;
      }

      // Token durumunu kontrol et
      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) {
        LoggingService.authLog('Background check: Cannot determine token expiration');
        return;
      }

      LoggingService.authLog('Background token status',
        details: 'Remaining: ${remainingTime.inMinutes} minutes, Should refresh: ${_jwtService.shouldRefreshToken(accessToken)}');

      // Smart token refresh - sadece kritik durumlarda background'da refresh
      if (_jwtService.isTokenInCriticalState(accessToken)) {
        LoggingService.authLog('Background critical refresh needed');
        await _performBackgroundRefresh();
      } else if (_jwtService.needsImmediateRefresh(accessToken)) {
        LoggingService.authLog('Background immediate refresh needed');
        await _performBackgroundRefresh();
      } else {
        LoggingService.authLog('Background check: Token is healthy',
          details: 'Remaining: ${remainingTime.inMinutes} minutes');
        // Normal refresh'i ana service'e bırak, background'da gereksiz yere refresh yapma
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'BackgroundTokenManager _performBackgroundTokenCheck');
    } finally {
      _isPerformingCheck = false;
    }
  }

  /// Background'da token refresh
  Future<void> _performBackgroundRefresh() async {
    try {
      LoggingService.authLog('Starting background token refresh');

      // Rate limiting - son başarılı refresh'den 3 dakika geçmemişse skip et (10K+ kullanıcı için)
      if (_lastSuccessfulRefresh != null &&
          DateTime.now().difference(_lastSuccessfulRefresh!).inMinutes < 3) {
        LoggingService.authLog('Background refresh skipped - rate limited');
        return;
      }

      // Token refresh service'i kullan
      final refreshSuccess = await _performTokenRefreshWithRetry();

      if (refreshSuccess) {
        _successfulRefreshCount++;
        _lastSuccessfulRefresh = DateTime.now();
        LoggingService.authLog('Background token refresh successful',
          details: 'Success count: $_successfulRefreshCount');
      } else {
        _failedRefreshCount++;
        LoggingService.authLog('Background token refresh failed',
          details: 'Failed count: $_failedRefreshCount');
      }

    } catch (e, stackTrace) {
      _failedRefreshCount++;
      LoggingService.logException(e, stackTrace, context: 'BackgroundTokenManager _performBackgroundRefresh');
    }
  }

  /// Retry logic ile token refresh
  Future<bool> _performTokenRefreshWithRetry() async {
    for (int attempt = 1; attempt <= AppConstants.maxTokenRefreshRetries; attempt++) {
      try {
        LoggingService.authLog('Background refresh attempt $attempt/${AppConstants.maxTokenRefreshRetries}');

        final refreshToken = await _storageService.getRefreshToken();
        if (refreshToken == null) {
          LoggingService.authLog('Background refresh failed: No refresh token');
          return false;
        }

        // Device info al
        DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
        if (deviceInfo == null) {
          final deviceService = DeviceService();
          deviceInfo = await deviceService.getDeviceInfo();
          await _storageService.saveDeviceInfo(deviceInfo);
        }
        final deviceInfoString = deviceInfo.toDeviceInfoString();

        // API çağrısı
        final apiService = ApiService();
        final response = await apiService.post<Map<String, dynamic>>(
          '/auth/refresh-token',
          data: {
            'refreshToken': refreshToken,
            'deviceInfo': deviceInfoString,
          },
          isAuthFree: true,
        );

        if (response.statusCode == 200 && response.data?['success'] == true) {
          final data = response.data!['data'];

          // Yeni token'ları kaydet
          await _storageService.saveAccessToken(data['token']);
          await _storageService.saveRefreshToken(data['refreshToken']);

          // User data'yı güncelle
          final userModel = _jwtService.decodeToken(data['token']);
          if (userModel != null) {
            await _storageService.saveUserData(userModel);
          }

          LoggingService.authLog('Background refresh attempt $attempt successful');
          return true;
        } else {
          LoggingService.authLog('Background refresh attempt $attempt failed',
            details: response.data?['message']);
        }

      } catch (e) {
        LoggingService.authLog('Background refresh attempt $attempt error',
          details: e.toString());
      }

      // Son deneme değilse bekle
      if (attempt < AppConstants.maxTokenRefreshRetries) {
        await Future.delayed(AppConstants.tokenRefreshRetryDelay);
      }
    }

    return false;
  }

  /// Background management'ı durdur
  void stopBackgroundManagement() {
    try {
      _backgroundTimer?.cancel();
      _backgroundTimer = null;
      _isBackgroundActive = false;
      _isPerformingCheck = false;

      LoggingService.authLog('Background token management stopped',
        details: 'Total checks: $_backgroundCheckCount, Successful refreshes: $_successfulRefreshCount, Failed refreshes: $_failedRefreshCount');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'BackgroundTokenManager stopBackgroundManagement');
    }
  }

  /// Background management'ı yeniden başlat
  void restartBackgroundManagement() {
    try {
      LoggingService.authLog('Restarting background token management');
      stopBackgroundManagement();
      startBackgroundManagement();
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'BackgroundTokenManager restartBackgroundManagement');
    }
  }

  /// Manuel background check tetikle
  Future<void> triggerManualBackgroundCheck() async {
    LoggingService.authLog('Manual background check triggered');
    await _performBackgroundTokenCheck();
  }

  /// Background manager durumunu al
  BackgroundTokenManagerStatus getStatus() {
    return BackgroundTokenManagerStatus(
      isActive: _isBackgroundActive,
      isPerformingCheck: _isPerformingCheck,
      lastTokenCheck: _lastTokenCheck,
      lastSuccessfulRefresh: _lastSuccessfulRefresh,
      backgroundCheckCount: _backgroundCheckCount,
      successfulRefreshCount: _successfulRefreshCount,
      failedRefreshCount: _failedRefreshCount,
    );
  }

  /// Debug bilgilerini al
  Map<String, dynamic> getDebugInfo() {
    return {
      'isActive': _isBackgroundActive,
      'isPerformingCheck': _isPerformingCheck,
      'lastTokenCheck': _lastTokenCheck?.toIso8601String(),
      'lastSuccessfulRefresh': _lastSuccessfulRefresh?.toIso8601String(),
      'backgroundCheckCount': _backgroundCheckCount,
      'successfulRefreshCount': _successfulRefreshCount,
      'failedRefreshCount': _failedRefreshCount,
      'checkInterval': AppConstants.backgroundTokenCheckInterval.inMinutes,
    };
  }

  /// Service'i dispose et
  void dispose() {
    LoggingService.authLog('BackgroundTokenManager disposing');
    stopBackgroundManagement();
  }
}

/// Background Token Manager Status
class BackgroundTokenManagerStatus {
  final bool isActive;
  final bool isPerformingCheck;
  final DateTime? lastTokenCheck;
  final DateTime? lastSuccessfulRefresh;
  final int backgroundCheckCount;
  final int successfulRefreshCount;
  final int failedRefreshCount;

  const BackgroundTokenManagerStatus({
    required this.isActive,
    required this.isPerformingCheck,
    this.lastTokenCheck,
    this.lastSuccessfulRefresh,
    required this.backgroundCheckCount,
    required this.successfulRefreshCount,
    required this.failedRefreshCount,
  });

  @override
  String toString() {
    return 'BackgroundTokenManagerStatus(isActive: $isActive, checks: $backgroundCheckCount, successful: $successfulRefreshCount, failed: $failedRefreshCount)';
  }
}
