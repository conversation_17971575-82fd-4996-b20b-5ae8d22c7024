/// Silent Token Refresh Service - GymKod Pro Mobile
///
/// Bu service kullanıcı fark etmeden background'da token refresh işlemlerini yapar.
/// App lifecycle'ında ve background'da çalışarak token'ların sürekli geçerli kalmasını <PERSON>ğlar.
/// Referans: Angular frontend'deki silent refresh pattern
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'services.dart';

/// Silent Token Refresh Service
/// Kullanıcı fark etmeden token'ları yeniler
class SilentTokenRefreshService {
  static final SilentTokenRefreshService _instance = SilentTokenRefreshService._internal();
  factory SilentTokenRefreshService() => _instance;
  SilentTokenRefreshService._internal();

  // Services
  final StorageService _storageService = StorageService();
  final JwtService _jwtService = JwtService();
  final ApiService _apiService = ApiService();

  // Timer management
  Timer? _backgroundTimer;
  Timer? _periodicCheckTimer;
  bool _isActive = false;
  bool _isRefreshing = false;
  DateTime? _lastRefreshAttempt;

  // Callbacks
  VoidCallback? _onTokenRefreshed;
  VoidCallback? _onRefreshFailed;

  /// Silent refresh service'i başlat
  Future<void> initialize({
    VoidCallback? onTokenRefreshed,
    VoidCallback? onRefreshFailed,
  }) async {
    try {
      LoggingService.authLog('SilentTokenRefreshService initializing');

      _onTokenRefreshed = onTokenRefreshed;
      _onRefreshFailed = onRefreshFailed;
      _isActive = true;

      // Periodic check timer'ı başlat
      await _startPeriodicTokenCheck();

      LoggingService.authLog('SilentTokenRefreshService initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService initialize');
    }
  }

  /// Periodic token check'i başlat
  Future<void> _startPeriodicTokenCheck() async {
    try {
      _periodicCheckTimer?.cancel();
      
      _periodicCheckTimer = Timer.periodic(
        AppConstants.tokenValidityCheckInterval,
        (_) => _performPeriodicTokenCheck(),
      );

      LoggingService.authLog('Periodic token check started', 
        details: 'Interval: ${AppConstants.tokenValidityCheckInterval.inMinutes} minutes');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService _startPeriodicTokenCheck');
    }
  }

  /// Periodic token kontrolü
  Future<void> _performPeriodicTokenCheck() async {
    if (!_isActive || _isRefreshing) return;

    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) {
        LoggingService.authLog('Periodic check: No access token found');
        return;
      }

      // Token durumunu kontrol et
      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) {
        LoggingService.authLog('Periodic check: Cannot determine token expiration');
        return;
      }

      LoggingService.authLog('Periodic token check',
        details: 'Remaining: ${remainingTime.inMinutes} minutes');

      // Token yenilenmesi gerekiyor mu?
      if (_jwtService.shouldRefreshToken(accessToken)) {
        LoggingService.authLog('Periodic check: Token needs refresh');
        await _performSilentRefresh();
      } else if (_jwtService.needsImmediateRefresh(accessToken)) {
        LoggingService.authLog('Periodic check: Token needs immediate refresh');
        await _performSilentRefresh();
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService _performPeriodicTokenCheck');
    }
  }

  /// Background'da sessiz token refresh
  Future<void> startBackgroundRefresh() async {
    if (!_isActive || _backgroundTimer != null) return;
    
    try {
      LoggingService.authLog('Starting background token refresh');
      
      _backgroundTimer = Timer.periodic(
        AppConstants.backgroundTokenCheckInterval,
        (_) => _performBackgroundRefresh(),
      );

      LoggingService.authLog('Background token refresh started', 
        details: 'Interval: ${AppConstants.backgroundTokenCheckInterval.inMinutes} minutes');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService startBackgroundRefresh');
    }
  }

  /// Background refresh işlemi
  Future<void> _performBackgroundRefresh() async {
    if (!_isActive || _isRefreshing) return;

    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) return;
      
      // Token'ın yenilenmesi gerekiyor mu?
      if (_jwtService.shouldRefreshToken(accessToken)) {
        LoggingService.authLog('Background refresh needed');
        await _performSilentRefresh();
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService _performBackgroundRefresh');
    }
  }

  /// Sessiz token refresh işlemi
  Future<bool> _performSilentRefresh() async {
    if (_isRefreshing || !_isActive) {
      LoggingService.authLog('Silent refresh skipped - already refreshing or inactive');
      return false;
    }

    // Rate limiting - son denemeden 2 dakika geçmemişse skip et (10K+ kullanıcı için)
    if (_lastRefreshAttempt != null &&
        DateTime.now().difference(_lastRefreshAttempt!).inMinutes < 2) {
      LoggingService.authLog('Silent refresh skipped - rate limited');
      return false;
    }

    try {
      _isRefreshing = true;
      _lastRefreshAttempt = DateTime.now();
      
      LoggingService.authLog('Starting silent token refresh');

      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        LoggingService.authLog('Silent refresh failed: No refresh token');
        _onRefreshFailed?.call();
        return false;
      }

      // Device info al
      DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
      if (deviceInfo == null) {
        final deviceService = DeviceService();
        deviceInfo = await deviceService.getDeviceInfo();
        await _storageService.saveDeviceInfo(deviceInfo);
      }
      final deviceInfoString = deviceInfo.toDeviceInfoString();

      // Retry logic ile refresh token API çağrısı
      bool refreshSuccess = false;
      for (int attempt = 1; attempt <= AppConstants.maxTokenRefreshRetries; attempt++) {
        try {
          LoggingService.authLog('Silent refresh attempt $attempt/${AppConstants.maxTokenRefreshRetries}');
          
          final response = await _apiService.post<Map<String, dynamic>>(
            '/auth/refresh-token',
            data: {
              'refreshToken': refreshToken,
              'deviceInfo': deviceInfoString,
            },
            isAuthFree: true,
          );

          if (response.statusCode == 200 && response.data?['success'] == true) {
            final data = response.data!['data'];
            
            // Yeni token'ları kaydet
            await _storageService.saveAccessToken(data['token']);
            await _storageService.saveRefreshToken(data['refreshToken']);

            // User data'yı güncelle
            final userModel = _jwtService.decodeToken(data['token']);
            if (userModel != null) {
              await _storageService.saveUserData(userModel);
            }

            LoggingService.authLog('Silent token refresh successful');
            refreshSuccess = true;
            break;
          } else {
            LoggingService.authLog('Silent refresh attempt $attempt failed', 
              details: response.data?['message']);
          }
        } catch (e) {
          LoggingService.authLog('Silent refresh attempt $attempt error', details: e.toString());
        }

        // Son deneme değilse bekle
        if (attempt < AppConstants.maxTokenRefreshRetries) {
          await Future.delayed(AppConstants.tokenRefreshRetryDelay);
        }
      }

      if (refreshSuccess) {
        _onTokenRefreshed?.call();
        return true;
      } else {
        LoggingService.authLog('Silent token refresh failed after all attempts');
        _onRefreshFailed?.call();
        return false;
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService _performSilentRefresh');
      _onRefreshFailed?.call();
      return false;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Background refresh'i durdur
  void stopBackgroundRefresh() {
    _backgroundTimer?.cancel();
    _backgroundTimer = null;
    LoggingService.authLog('Background token refresh stopped');
  }

  /// Service'i durdur
  void stop() {
    try {
      LoggingService.authLog('SilentTokenRefreshService stopping');
      
      _isActive = false;
      _periodicCheckTimer?.cancel();
      _periodicCheckTimer = null;
      stopBackgroundRefresh();
      _isRefreshing = false;
      
      LoggingService.authLog('SilentTokenRefreshService stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService stop');
    }
  }

  /// Service'i yeniden başlat
  Future<void> restart() async {
    try {
      LoggingService.authLog('SilentTokenRefreshService restarting');
      
      stop();
      await initialize(
        onTokenRefreshed: _onTokenRefreshed,
        onRefreshFailed: _onRefreshFailed,
      );
      
      LoggingService.authLog('SilentTokenRefreshService restarted');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SilentTokenRefreshService restart');
    }
  }

  /// Manuel token refresh tetikle
  Future<bool> triggerManualRefresh() async {
    LoggingService.authLog('Manual token refresh triggered');
    return await _performSilentRefresh();
  }

  /// Service durumunu kontrol et
  bool get isActive => _isActive;
  bool get isRefreshing => _isRefreshing;
  DateTime? get lastRefreshAttempt => _lastRefreshAttempt;

  /// Service'i dispose et
  void dispose() {
    LoggingService.authLog('SilentTokenRefreshService disposing');
    stop();
  }
}
