mobilde refresh token sistemi düzgün mü araştırmanı istiyorum. şimdi login olduğumda  ve login olduğumda uygulamada 3 dakika gezdim ve vscode terminalde bu veriler dönüyor ve sonra uygulamadan çıkıyorum işte dönen veriler. 

PS C:\Users\<USER>\Desktop\GymProject\GymProjectMobile> flutter run
Launching lib\main.dart on M2010J19SG in debug mode...
Running Gradle task 'assembleDebug'...                              4,4s
√ Built build\app\outputs\flutter-apk\app-debug.apk
Installing build\app\outputs\flutter-apk\app-debug.apk...           7,9s
I/flutter (16504): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(60)] Using the Impeller rendering backend (Vulkan).
W/Looper  (16504): PerfMonitor looperActivity : package=com.example.gymprojectmobile/.MainActivity time=0ms latency=4086ms running=0ms  procState=-1  historyMsgCount=4 (msgIndex=3 wall=610ms seq=3 h=android.app.ActivityThread$H w=110) (msgIndex=4 wall=2906ms seq=4 h=android.app.ActivityThread$H w=159)
I/Choreographer(16504): Skipped 210 frames!  The application may be doing too much work on its main thread.
I/AdrenoGLES-0(16504): QUALCOMM build                   : 1a315fd02c, I090e68fbb7
I/AdrenoGLES-0(16504): Build Date                       : 12/01/21
I/AdrenoGLES-0(16504): OpenGL ES Shader Compiler Version: EV031.32.02.16
I/AdrenoGLES-0(16504): Local Branch                     :
I/AdrenoGLES-0(16504): Remote Branch                    :
I/AdrenoGLES-0(16504): Remote Branch                    :
I/AdrenoGLES-0(16504): Reconstruct Branch               :
I/AdrenoGLES-0(16504): Build Config                     : S P 10.0.7 AArch64
I/AdrenoGLES-0(16504): Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
I/AdrenoGLES-0(16504): PFP: 0x016ee197, ME: 0x00000000
D/SurfaceView(16504): UPDATE null, mIsCastMode = false
D/hw-ProcessState(16504): Binder ioctl to enable oneway spam detection failed: Invalid argument
W/Looper  (16504): PerfMonitor doFrame : time=279ms vsyncFrame=0 latency=3502ms procState=-1 historyMsgCount=8 (msgIndex=3 wall=279ms seq=9 late=3502ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=4 wall=2906ms seq=4 h=android.app.ActivityThread$H w=159)
Syncing files to device M2010J19SG...                              122ms

Flutter run key commands.
r Hot reload.
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on M2010J19SG is available at: http://127.0.0.1:10877/gHXxdlio8gs=/
I/flutter (16504): [2025-06-13T14:24:29.918506] 🐛 [DEBUG] [GymKodPro] [STATE] State: AuthNotifier - Initializing
W/MIUIScout App(16504): Enter APP_SCOUT_SLOW state
I/ymprojectmobil(16504): Thread[2,tid=16521,WaitingInMainSignalCatcherLoop,Thread*=0xb4000073e4402000,peer=0x14447ce8,"Signal Catcher"]: reacting to signal 3
I/ymprojectmobil(16504):
I/ymprojectmobil(16504): Wrote stack traces to tombstoned
I/flutter (16504): [2025-06-13T14:24:31.197809] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Redirect check - Route: / - Auth: false, Initialized: false, RequirePasswordChange: false
I/flutter (16504): [2025-06-13T14:24:31.544408] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Navigate to splash - Route: /
I/flutter (16504): [2025-06-13T14:24:31.561597] 🐛 [DEBUG] [GymKodPro] [WIDGET] Widget: SplashPage - initState
I/flutter (16504): [2025-06-13T14:24:31.564935] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:31.567632] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:32.527853] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:32.529060] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
The Flutter DevTools debugger and profiler on M2010J19SG is available at: http://127.0.0.1:9102?uri=http://127.0.0.1:10877/gHXxdlio8gs=/
E/ymprojectmobil(16504): open libmigui.so failed! dlopen - dlopen failed: library "libmigui.so" not found
I/Choreographer(16504): Skipped 221 frames!  The application may be doing too much work on its main thread.
D/DecorView[](16504): getWindowModeFromSystem  windowmode is 1
D/DecorView[](16504): updateDecorCaptionStatus displayWindowDecor is false
D/SurfaceView(16504): UPDATE Surface(name=SurfaceView[com.example.gymprojectmobile/com.example.gymprojectmobile.MainActivity])/@0x4b4229d, mIsProjectionMode = false
D/ymprojectmobil(16504): MiuiProcessManagerServiceStub setSchedFifo
I/MiuiProcessManagerImpl(16504): setSchedFifo pid:16504, mode:3
W/Looper  (16504): PerfMonitor doFrame : time=131ms vsyncFrame=0 latency=3688ms procState=-1 historyMsgCount=10 (msgIndex=1 wall=131ms seq=19 late=3688ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
I/OpenGLRenderer(16504): Davey! duration=3814ms; Flags=1, FrameTimelineVsyncId=17147402, IntendedVsync=295095377736925, Vsync=295099061070111, InputEventId=0, HandleInputStart=295099066014139, AnimationStart=295099066018201, PerformTraversalsStart=295099066020909, DrawStart=295099096158774, FrameDeadline=295095394403591, FrameInterval=295099065613878, FrameStartTime=16666666, SyncQueued=295099112344034, SyncStart=295099112471899, IssueDrawCommandsStart=295099130623201, SwapBuffers=295099188596170, FrameCompleted=295099192543461, DequeueBufferDuration=8415781, QueueBufferDuration=1407292, GpuCompleted=295099192543461, SwapBuffersCompleted=295099191201014, DisplayPresentTime=489632387025,
I/flutter (16504): [2025-06-13T14:24:32.826652] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:32.827372] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:33.050376] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:33.054338] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
D/DecorView[](16504): onWindowFocusChanged hasWindowFocus true
I/flutter (16504): [2025-06-13T14:24:33.256982] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:33.258221] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:33.460500] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:33.461430] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:33.664190] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:33.664957] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:33.676852] ℹ️ [INFO] [GymKodPro] [THEME_SERVICE] Saved theme loaded: dark
I/flutter (16504): [2025-06-13T14:24:34.025018] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:34.026042] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:34.250637] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:34.251789] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
D/ProfileInstaller(16504): Installing profile for com.example.gymprojectmobile
I/flutter (16504): [2025-06-13T14:24:34.489727] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:34.491018] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:34.692660] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:34.693688] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:34.895430] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:34.896190] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:34.960693] 🐛 [DEBUG] [GymKodPro] [STATE] State: AuthNotifier - Initialized as not authenticated
I/flutter (16504): [2025-06-13T14:24:35.097419] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (16504): [2025-06-13T14:24:35.098093] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: true, Authenticated: false
I/flutter (16504): [2025-06-13T14:24:35.899601] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Splash redirect - Route: /auth/login - User not authenticated
I/flutter (16504): [2025-06-13T14:24:35.921898] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Redirect check - Route: /auth/login - Auth: false, Initialized: true, RequirePasswordChange: false
I/flutter (16504): [2025-06-13T14:24:35.978494] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Navigate to login - Route: /auth/login
I/flutter (16504): [2025-06-13T14:24:35.988257] 🐛 [DEBUG] [GymKodPro] [WIDGET] Widget: LoginPage - initState
D/InputMethodManager(16504): showSoftInput() view=io.flutter.embedding.android.FlutterView{758fcc VFE...... .F....ID 0,0-1080,2296 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(16504): The input method toggled cursor monitoring on
D/InputMethodManager(16504): showSoftInput() view=io.flutter.embedding.android.FlutterView{758fcc VFE...... .F...... 0,0-1080,2296 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
D/InsetsController(16504): show(ime(), fromIme=true)
D/InsetsController(16504): show(ime(), fromIme=true)
W/RenderInspector(16504): DequeueBuffer time out on com.example.gymprojectmobile/com.example.gymprojectmobile.MainActivity, count=1, avg=27 ms, max=27 ms.
D/InputMethodManager(16504): showSoftInput() view=io.flutter.embedding.android.FlutterView{758fcc VFE...... .F...... 0,0-1080,2296 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
D/InputConnectionAdaptor(16504): The input method toggled cursor monitoring on
D/InputConnectionAdaptor(16504): The input method toggled cursor monitoring off
D/InputConnectionAdaptor(16504): The input method toggled cursor monitoring on
D/InsetsController(16504): show(ime(), fromIme=true)
D/InsetsController(16504): show(ime(), fromIme=true)
I/flutter (16504): [2025-06-13T14:24:44.336695] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Login attempt - Email: <EMAIL>
I/flutter (16504): [2025-06-13T14:24:44.356374] 🐛 [DEBUG] [GymKodPro] [STATE] State: AuthNotifier - Login attempt - State: <EMAIL>
I/flutter (16504): [2025-06-13T14:24:44.584805] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Login attempt - Email: <EMAIL>
I/flutter (16504): [2025-06-13T14:24:44.753874] 🐛 [DEBUG] [GymKodPro] [API] API Request: POST /auth/login
I/flutter (16504): [2025-06-13T14:24:44.760450] 🐛 [DEBUG] [GymKodPro] [API] Request Data: {loginDto: Instance of 'LoginDto', deviceInfo: Android|SKQ1.211202.001|Redmi M2010J19SG|Android 12 (API 31)|1.0.0}
I/flutter (16504): [2025-06-13T14:24:44.762026] 🐛 [DEBUG] [GymKodPro] [API_DEBUG] Request headers: {Content-Type: application/json, Accept: application/json}
I/flutter (16504): [2025-06-13T14:24:44.763521] 🐛 [DEBUG] [GymKodPro] [API_DEBUG] Request base URL: http://192.168.1.102:5165/api/
D/InputConnectionAdaptor(16504): The input method toggled cursor monitoring off
W/RenderInspector(16504): QueueBuffer time out on com.example.gymprojectmobile/com.example.gymprojectmobile.MainActivity, count=11, avg=11 ms, max=33 ms.
I/flutter (16504): [2025-06-13T14:24:45.445291] 🐛 [DEBUG] [GymKodPro] [API] API Response: POST /auth/login - Status: 200
I/flutter (16504): [2025-06-13T14:24:45.445941] 🐛 [DEBUG] [GymKodPro] [API] Response Data: {success: true, message: Giriş başarılı, requirePasswordChange: false, data: {token: eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjkiLCJlbWFpbCI6ImNleWxpbkBnbWFpbC5jb20iLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiQ0VZTMSwTiDFnklSTEFLIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoibWVtYmVyIiwibmJmIjoxNzQ5ODEzODg0LCJleHAiOjE3NDk4MTQwMDQsImlzcyI6Imh0dHBzOi8vYWRtaW4uZ3lta29kLmNvbSIsImF1ZCI6Imh0dHBzOi8vYXBpLmd5bWtvZC5jb20ifQ.IJEsacO-_AL_ImWXvHDCF38i6X37KWSGLz9t4phxcOpqtXsO2PgYMAHjg6jH0NLyRLO-rbxEeGKXbBi-CMJfGg, refreshToken: a0VqtLvjQplgp5ErNOCC5azgcZp5WBBxIzikFXexn28=, expiration: 2025-06-13T14:26:44.2211979+03:00}}
I/flutter (16504): [2025-06-13T14:24:45.449242] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Raw login response - Response: {success: true, message: Giriş başarılı, requirePasswordChangee: false, data: {token: eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjkiLCJlbWFpbCI6ImNleWxpbkBnbWFpbC5jb20iLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiQ0VZTMSwTiDFnklSTEFLIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoibWVtYmVyIiwibmJmIjoxNzQ5ODEzODg0LCJleHAiOjE3NDk4MTQwMDQsImlzcyI6Imh0dHBzOi8vYWRtaW4uZ3lta29kLmNvbSIsImF1ZCI6Imh0dHBzOi8vYXBpLmd5bWtvZC5jb20ifQ.IJEsacO-_AL_ImWXvHDCF38i6X37KWSGLz9t4phxcOpqtXsO2PgYMAHjg6jH0NLyRLO-rbxEeGKXbBi-CMJfGg, refreshToken: a0VqtLvjQplgp5ErNOCC5azgcZp5WBBxIzikFXexn28=, expiration: 2025-06-13T14:26:44.2211979+03:00}}
I/flutter (16504): [2025-06-13T14:24:45.471647] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Login response parsed - Success: true, Message: Giriş başarılı, RequirePasswordChange: false
I/flutter (16504): [2025-06-13T14:24:46.098851] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Login successful - User: CEYLİN ŞIRLAK, RequirePasswordChange: false
I/flutter (16504): [2025-06-13T14:24:46.373887] ℹ️ [INFO] [GymKodPro] [PROFILE_IMAGE] User logged in, refreshing profile image
I/flutter (16504): [2025-06-13T14:24:46.432157] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Starting token refresh service
I/flutter (16504): [2025-06-13T14:24:46.433480] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: TokenRefreshService initializing
I/flutter (16504): [2025-06-13T14:24:46.753595] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Token refresh timer started
I/flutter (16504): [2025-06-13T14:24:46.755108] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: TokenRefreshService initialized successfully
I/flutter (16504): [2025-06-13T14:24:46.755901] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Token refresh service started successfully
I/flutter (16504): [2025-06-13T14:24:46.756813] 🐛 [DEBUG] [GymKodPro] [STATE] State: AuthNotifier - Login successful - State: User: CEYLİN ŞIRLAK, RequirePasswordChange: false
I/flutter (16504): [2025-06-13T14:24:46.904626] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Login success - checking password change requirement - Route: RequirePasswordChange: false, AuthState: AuthState(isAuthenticated: true, user: CEYLİN ŞIRLAK, isLoading: false, error: null, isInitialized: true, requirePasswordChange: false)
I/flutter (16504): [2025-06-13T14:24:46.905328] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Login success - role-based navigation - Route: User role: member
I/flutter (16504): [2025-06-13T14:24:46.905945] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Login success redirect - member - Route: /member-main
I/flutter (16504): [2025-06-13T14:24:46.919225] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Redirect check - Route: /member-main - Auth: true, Initialized: true, RequirePasswordChange: false
I/flutter (16504): [2025-06-13T14:24:46.969343] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Navigate to member main - Route: /member-main
I/flutter (16504): [2025-06-13T14:24:46.972769] ℹ️ [INFO] [GymKodPro] [MEMBER_LAYOUT] Member main layout initialized
I/flutter (16504): [2025-06-13T14:24:47.424236] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRCode - Loading QR code
I/flutter (16504): [2025-06-13T14:24:47.428643] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Member QR repository request
I/flutter (16504): [2025-06-13T14:24:47.431765] 🐛 [DEBUG] [GymKodPro] [API] API: Member QR request - /member/getmemberqrbyuserid
I/flutter (16504): [2025-06-13T14:24:47.436203] ℹ️ [INFO] [GymKodPro] [QR_PROFILE] QR Page Init - Current Image URL: null
I/flutter (16504): [2025-06-13T14:24:47.436894] ℹ️ [INFO] [GymKodPro] [QR_PROFILE] QR Page Init - Loading profile image...

══╡ EXCEPTION CAUGHT BY IMAGE RESOURCE SERVICE ╞════════════════════════════════════════════════════
The following HttpExceptionWithStatus was thrown resolving an image codec:
HttpException: Invalid statusCode: 404, uri =
http://192.168.1.102:5165/api/user/profile-image/9?t=1749812600649

When the exception was thrown, this was the stack

Image provider:
  CachedNetworkImageProvider("http://192.168.1.102:5165/api/user/profile-image/9?t=1749812600649",
  scale: 1.0)
Image key:
  CachedNetworkImageProvider("http://192.168.1.102:5165/api/user/profile-image/9?t=1749812600649",
  scale: 1.0)
════════════════════════════════════════════════════════════════════════════════════════════════════

I/flutter (16504): [2025-06-13T14:24:48.560677] 🐛 [DEBUG] [GymKodPro] [API] API Request: GET /member/getmemberqrbyuserid
I/flutter (16504): [2025-06-13T14:24:48.561449] 🐛 [DEBUG] [GymKodPro] [API_DEBUG] Request headers: {Content-Type: application/json, Accept: application/json, Authorization: Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjkiLCJlbWFpbCI6ImNleWxpbkBnbWFpbC5jb20iLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiQ0VZTMSwTiDFnklSTEFLIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoibWVtYmVyIiwibmJmIjoxNzQ5ODEzODg0LCJleHAiOjE3NDk4MTQwMDQsImlzcyI6Imh0dHBzOi8vYWRtaW4uZ3lta29kLmNvbSIsImF1ZCI6Imh0dHBzOi8vYXBpLmd5bWtvZC5jb20ifQ.IJEsacO-_AL_ImWXvHDCF38i6X37KWSGLz9t4phxcOpqtXsO2PgYMAHjg6jH0NLyRLO-rbxEeGKXbBi-CMJfGg}
I/flutter (16504): [2025-06-13T14:24:48.562014] 🐛 [DEBUG] [GymKodPro] [API_DEBUG] Request base URL: http://192.168.1.102:5165/api/
I/flutter (16504): [2025-06-13T14:24:48.650126] 🐛 [DEBUG] [GymKodPro] [API] API Response: GET /member/getmemberqrbyuserid - Status: 200
I/flutter (16504): [2025-06-13T14:24:48.653675] 🐛 [DEBUG] [GymKodPro] [API] Response Data: {data: {name: CEYLİN ŞIRLAK, scanNumber: PV3U4N9RC7GM5DLQKYJ9KS49RWFN93TNTPB8MKLZ4YEFYG8T34LX222MMDWRXFT72LCSTH8X3JPDMU22B8KNW6C9G9V626M8VADB2Y5RMF8L2AGQXUQLNRZ2Q8KPPLMDYRBRX3S846GSBK5Z4GQZ9JP2ZFDRAWG8DZ8B44KF8JNVML48RPRXJFV8JCU6D4QLN63X9S9LUCDBHJHDQJHBLSPLKL7APPYLZCEMCKA, remainingDays: Fitness: 0 Gün, memberships: [{branch: Fitness, startDate: 2025-05-08T00:00:00, endDate: 2025-06-07T00:00:00, remainingDays: 0}], isFrozen: false, freezeEndDate: null, message: null, phoneNumber: 05363304276}, success: true, message: Üyeliğiniz aktif durumdadır.}
I/flutter (16504): [2025-06-13T14:24:48.662708] 🐛 [DEBUG] [GymKodPro] [API] API: Member QR success - Üyeliğiniz aktif durumdadır.
I/flutter (16504): [2025-06-13T14:24:48.665362] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Member QR repository success - Name: CEYLİN ŞIRLAK, Phone: 05363304276
I/flutter (16504): [2025-06-13T14:24:48.666654] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRCode - QR code loaded successfully - State: Name: CEYLİN ŞIRLAK, Phone: 05363304276     
I/flutter (16504): [2025-06-13T14:24:48.668758] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRCode - Starting QR timer (5 minutes)
I/flutter (16504): [2025-06-13T14:24:48.671254] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRTimer - Starting QR timer (5 minutes)
I/flutter (16504): [2025-06-13T14:24:48.673359] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRTimer - Timer stopped
I/flutter (16504): [2025-06-13T14:24:48.677155] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRTimer - QR timer started successfully - State: Duration: 5:00
I/flutter (16504): [2025-06-13T14:24:48.679144] 🐛 [DEBUG] [GymKodPro] [STATE] State: QRCode - Rate limit set for 3 minutes
I/flutter (16504): [2025-06-13T14:25:44.002695] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Token refresh timer triggered
I/flutter (16504): [2025-06-13T14:25:44.006569] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Starting token refresh
I/flutter (16504): [2025-06-13T14:25:44.295025] 🐛 [DEBUG] [GymKodPro] [API] API Request: POST /auth/refresh-token
I/flutter (16504): [2025-06-13T14:25:44.295608] 🐛 [DEBUG] [GymKodPro] [API] Request Data: {refreshToken: a0VqtLvjQplgp5ErNOCC5azgcZp5WBBxIzikFXexn28=, deviceInfo: Unknown Device}
I/flutter (16504): [2025-06-13T14:25:44.296211] 🐛 [DEBUG] [GymKodPro] [API_DEBUG] Request headers: {Content-Type: application/json, Accept: application/json}
I/flutter (16504): [2025-06-13T14:25:44.296676] 🐛 [DEBUG] [GymKodPro] [API_DEBUG] Request base URL: http://192.168.1.102:5165/api/
I/flutter (16504): [2025-06-13T14:25:44.357679] 🐛 [DEBUG] [GymKodPro] [API] API Response: POST /auth/refresh-token - Status: 200
I/flutter (16504): [2025-06-13T14:25:44.358260] 🐛 [DEBUG] [GymKodPro] [API] Response Data: {success: true, message: Token Yenilendi, data: {token: eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTUxMiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjkiLCJlbWFpbCI6ImNleWxpbkBnbWFpbC5jb20iLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiQ0VZTMSwTiDFnklSTEFLIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9yb2xlIjoibWVtYmVyIiwibmJmIjoxNzQ5ODEzOTQzLCJleHAiOjE3NDk4MTQwNjMsImlzcyI6Imh0dHBzOi8vYWRtaW4uZ3lta29kLmNvbSIsImF1ZCI6Imh0dHBzOi8vYXBpLmd5bWtvZC5jb20ifQ.fVD9MtNjDQ1m4jPZbhybep6snuXGq76ugRzsqSVQlwtnvvx96U7aY8pTIKLK6OrDovSuLTt0dW0sXHhPwo1MOw, refreshToken: Jd/QFNOsumNLxq3tmyVFiUmxkHBXA4uIqdmmxtBAeGA=, expiration: 2025-06-13T14:27:43.3401603+03:00}}
I/flutter (16504): [2025-06-13T14:25:44.660575] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Token refreshed successfully
I/flutter (16504): [2025-06-13T14:25:44.661589] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Token refreshed by service - updating user data
I/flutter (16504): [2025-06-13T14:25:44.867604] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: User data refreshed from token - CEYLİN ŞIRLAK
I/flutter (16504): [2025-06-13T14:25:45.064056] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Token refresh timer started
D/DecorView[](16504): onWindowFocusChanged hasWindowFocus false
W/Looper  (16504): PerfMonitor doFrame : time=436ms vsyncFrame=0 latency=1ms procState=-1 historyMsgCount=1 (msgIndex=1 wall=436ms seq=242 late=1ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
W/Looper  (16504): PerfMonitor looperActivity : package=com.example.gymprojectmobile/.MainActivity time=15ms latency=429ms running=0ms  procState=-1  historyMsgCount=5 (msgIndex=3 wall=436ms seq=242 late=1ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=4 wall=436ms seq=242 late=1ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)
Lost connection to device.

5 dakika sonra tekrar girdiğimde de bu veriler dönüyor ve login ekranına atılıyorum. normalde frontend sistemi gibi kurmaya çalıştım ama uygulamadan çıkıp tekrar girdiğimde tokenımın yenilenip devam etmesi gerekirken uygulamaya girince geçersiz oturum hatası alıyorum işte 5 dakika sonra tekrar uygulamaya girmeye çalışırken flutter run yapıyorum ve sonra bu kodlar dönüyor

PS C:\Users\<USER>\Desktop\GymProject\GymProjectMobile> flutter run
Launching lib\main.dart on M2010J19SG in debug mode...
Running Gradle task 'assembleDebug'...                              4,2s
√ Built build\app\outputs\flutter-apk\app-debug.apk
Installing build\app\outputs\flutter-apk\app-debug.apk...           6,6s
I/flutter (18874): [IMPORTANT:flutter/shell/platform/android/android_context_vk_impeller.cc(60)] Using the Impeller rendering backend (Vulkan).
I/AdrenoGLES-0(18874): QUALCOMM build                   : 1a315fd02c, I090e68fbb7
I/AdrenoGLES-0(18874): Build Date                       : 12/01/21
I/AdrenoGLES-0(18874): OpenGL ES Shader Compiler Version: EV031.32.02.16
I/AdrenoGLES-0(18874): Local Branch                     :
I/AdrenoGLES-0(18874): Remote Branch                    :
I/AdrenoGLES-0(18874): Remote Branch                    :
I/AdrenoGLES-0(18874): Reconstruct Branch               :
I/AdrenoGLES-0(18874): Build Config                     : S P 10.0.7 AArch64
I/AdrenoGLES-0(18874): Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
D/SurfaceView(18874): UPDATE null, mIsCastMode = false
I/AdrenoGLES-0(18874): PFP: 0x016ee197, ME: 0x00000000
D/hw-ProcessState(18874): Binder ioctl to enable oneway spam detection failed: Invalid argument
W/Looper  (18874): PerfMonitor doFrame : time=274ms vsyncFrame=0 latency=3689ms procState=-1 historyMsgCount=7 (msgIndex=1 wall=274ms seq=8 late=3689ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=2 wall=274ms seq=8 late=3689ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=3 wall=274ms seq=8 late=3689ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=4 wall=3123ms seq=4 h=android.app.ActivityThread$H w=159)
Syncing files to device M2010J19SG...                              134ms

Flutter run key commands.
r Hot reload.
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).

A Dart VM Service on M2010J19SG is available at: http://127.0.0.1:10550/MNbX0S3qu1I=/
I/flutter (18874): [2025-06-13T14:28:35.677426] 🐛 [DEBUG] [GymKodPro] [STATE] State: AuthNotifier - Initializing
W/MIUIScout App(18874): Enter APP_SCOUT_SLOW state
I/ymprojectmobil(18874): Thread[2,tid=18899,WaitingInMainSignalCatcherLoop,Thread*=0xb4000073e4402000,peer=0x14447da8,"Signal Catcher"]: reacting to signal 3
I/ymprojectmobil(18874):
I/ymprojectmobil(18874): Wrote stack traces to tombstoned
I/flutter (18874): [2025-06-13T14:28:36.971398] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Redirect check - Route: / - Auth: false, Initialized: false, RequirePasswordChange: false
I/flutter (18874): [2025-06-13T14:28:37.325546] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Navigate to splash - Route: /
I/flutter (18874): [2025-06-13T14:28:37.343283] 🐛 [DEBUG] [GymKodPro] [WIDGET] Widget: SplashPage - initState
I/flutter (18874): [2025-06-13T14:28:37.346724] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:37.348781] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:38.263580] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:38.264679] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
The Flutter DevTools debugger and profiler on M2010J19SG is available at: http://127.0.0.1:9102?uri=http://127.0.0.1:10550/MNbX0S3qu1I=/
E/ymprojectmobil(18874): open libmigui.so failed! dlopen - dlopen failed: library "libmigui.so" not found
I/Choreographer(18874): Skipped 216 frames!  The application may be doing too much work on its main thread.
D/DecorView[](18874): getWindowModeFromSystem  windowmode is 1
D/DecorView[](18874): updateDecorCaptionStatus displayWindowDecor is false
D/SurfaceView(18874): UPDATE Surface(name=SurfaceView[com.example.gymprojectmobile/com.example.gymprojectmobile.MainActivity])/@0xcc46d74, mIsProjectionMode = false
D/ymprojectmobil(18874): MiuiProcessManagerServiceStub setSchedFifo
I/MiuiProcessManagerImpl(18874): setSchedFifo pid:18874, mode:3
W/Looper  (18874): PerfMonitor doFrame : time=96ms vsyncFrame=0 latency=3600ms procState=-1 historyMsgCount=10 (msgIndex=1 wall=96ms seq=18 late=3600ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver) (msgIndex=4 wall=52ms seq=11 h=android.view.ViewRootImpl$ViewRootHandler c=android.view.ViewRootImpl$4)
I/OpenGLRenderer(18874): Davey! duration=3693ms; Flags=1, FrameTimelineVsyncId=17165380, IntendedVsync=295341190618401, Vsync=295344790618257, InputEventId=0, HandleInputStart=295344791420034, AnimationStart=295344791424305, PerformTraversalsStart=295344791426909, DrawStart=295344815447066, FrameDeadline=295341207285067, FrameInterval=295344791041389, FrameStartTime=16666666, SyncQueued=295344823098159, SyncStart=295344823258784, IssueDrawCommandsStart=295344834365295, SwapBuffers=295344879941024, FrameCompleted=295344884057795, DequeueBufferDuration=7509739, QueueBufferDuration=1071093, GpuCompleted=295344884057795, SwapBuffersCompleted=295344882448003, DisplayPresentTime=489627178193,
I/flutter (18874): [2025-06-13T14:28:38.510891] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:38.511929] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:38.751523] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:38.752817] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
D/DecorView[](18874): onWindowFocusChanged hasWindowFocus true
I/flutter (18874): [2025-06-13T14:28:38.963625] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:38.964829] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:39.174062] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:39.175562] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:39.377505] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:39.378379] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:39.494877] ℹ️ [INFO] [GymKodPro] [THEME_SERVICE] Saved theme loaded: dark
I/flutter (18874): [2025-06-13T14:28:39.676327] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:39.677123] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:39.922512] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:39.924325] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:40.126519] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:40.127525] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:40.347753] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:40.349307] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:40.555803] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:40.556749] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
D/ProfileInstaller(18874): Installing profile for com.example.gymprojectmobile
I/flutter (18874): [2025-06-13T14:28:40.759088] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:40.761313] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: false, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:40.891488] 🐛 [DEBUG] [GymKodPro] [STATE] State: AuthNotifier - Initialized as not authenticated
I/flutter (18874): [2025-06-13T14:28:40.964196] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth check started
I/flutter (18874): [2025-06-13T14:28:40.965606] ℹ️ [INFO] [GymKodPro] [AUTH] Auth: Splash: Auth state checked - Initialized: true, Authenticated: false
I/flutter (18874): [2025-06-13T14:28:41.777267] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Splash redirect - Route: /auth/login - User not authenticated
I/flutter (18874): [2025-06-13T14:28:41.789988] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Redirect check - Route: /auth/login - Auth: false, Initialized: true, RequirePasswordChange: false
I/flutter (18874): [2025-06-13T14:28:41.872838] 🐛 [DEBUG] [GymKodPro] [NAV] Navigation: Navigate to login - Route: /auth/login
I/flutter (18874): [2025-06-13T14:28:41.885271] 🐛 [DEBUG] [GymKodPro] [WIDGET] Widget: LoginPage - initState
W/Activity(18874): PerfMonitor: Slow Operation: Activity com.example.gymprojectmobile/.MainActivity onPause took 139ms
D/DecorView[](18874): onWindowFocusChanged hasWindowFocus false
W/Looper  (18874): PerfMonitor doFrame : time=85ms vsyncFrame=0 latency=343ms procState=-1 historyMsgCount=2 (msgIndex=1 wall=85ms seq=75 late=343ms h=android.view.Choreographer$FrameHandler c=android.view.Choreographer$FrameDisplayEventReceiver)