/// Member Main Layout - GymKod Pro Mobile
///
/// Bu layout member r<PERSON><PERSON><PERSON><PERSON> kullanıcılar için ana sayfa yapısını sağlar.
/// Bottom navigation ile QR kod ve antrenman programı sayfaları arasında geçiş yapar.
///
/// RESPONSIVE DESIGN:
/// - Responsive bottom navigation height ve icon sizing
/// - Responsive page transitions ve animations
/// - Responsive safe area padding
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../providers/bottom_navigation_provider.dart';
import '../widgets/member_bottom_navigation.dart';
import 'qr_code_page.dart';
import 'workout_program_page.dart';
import 'member_profile_page.dart';

/// Member Main Layout
/// Member rolündeki kullanıcılar için ana layout
class MemberMainLayout extends ConsumerStatefulWidget {
  const MemberMainLayout({super.key});

  @override
  ConsumerState<MemberMainLayout> createState() => _MemberMainLayoutState();
}

class _MemberMainLayoutState extends ConsumerState<MemberMainLayout> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    LoggingService.info('Member main layout initialized', tag: 'MEMBER_LAYOUT');

    // Bottom navigation provider'ı sıfırla (doğru tab'da başlasın)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(bottomNavigationProvider.notifier).changeTab(0);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          body: SafeArea(
            // Responsive safe area padding
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                // Sayfa değiştiğinde provider'ı güncelle
                ref.read(bottomNavigationProvider.notifier).changeTab(index);
              },
              // Kaydırma devre dışı - sadece butonlarla geçiş
              physics: const NeverScrollableScrollPhysics(),
              children: const [
                // QR Code Page (Index 0)
                QRCodePage(),

                // Workout Program Page (Index 1)
                WorkoutProgramPage(),

                // Member Profile Page (Index 2)
                MemberProfilePage(),
              ],
            ),
          ),

          // Responsive Bottom Navigation
          bottomNavigationBar: MemberBottomNavigation(
            onTap: (index) {
              // Bottom navigation'a tıklandığında sayfayı değiştir
              _animateToPage(index);
            },
          ),
        );
      },
    );
  }

  /// Sayfayı animate ederek değiştir (responsive animation duration)
  void _animateToPage(int index) {
    if (_pageController.hasClients) {
      final width = MediaQuery.of(context).size.width;
      final deviceType = ScreenSize.getDeviceType(width);

      // Responsive animation duration
      final duration = deviceType == DeviceType.mobile
        ? const Duration(milliseconds: 250)  // Daha hızlı mobile'da
        : const Duration(milliseconds: 300);  // Normal tablet/desktop'ta

      _pageController.animateToPage(
        index,
        duration: duration,
        curve: Curves.easeInOut,
      );
    }
  }
}
