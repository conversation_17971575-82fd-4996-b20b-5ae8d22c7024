/// JWT Service - GymKod Pro Mobile
/// 
/// Bu service JWT token'ları decode eder ve validate eder.
/// Referans: Angular frontend'deki JWT handling
library;

import 'dart:convert';
import 'package:jwt_decoder/jwt_decoder.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import 'logging_service.dart';

/// JWT Token Service - Optimize edildi
/// Angular frontend'deki JWT service'e benzer
class JwtService {
  // Singleton pattern
  static final JwtService _instance = JwtService._internal();
  factory JwtService() => _instance;
  JwtService._internal();

  // Token cache (performans optimizasyonu)
  String? _cachedToken;
  UserModel? _cachedUser;
  bool? _cachedTokenValid;
  DateTime? _cacheTime;

  /// Cache'i temizle
  void _clearCache() {
    _cachedToken = null;
    _cachedUser = null;
    _cachedTokenValid = null;
    _cacheTime = null;
  }

  /// Cache'in geçerli olup olmadığını kontrol et (5 dakika)
  bool _isCacheValid() {
    if (_cacheTime == null) return false;
    return DateTime.now().difference(_cacheTime!).inMinutes < 5;
  }

  /// JWT token'ı decode et ve UserModel döndür - Cache'li
  /// Angular frontend'deki token decode işlemine benzer
  UserModel? decodeToken(String token) {
    try {
      // Cache kontrolü (performans optimizasyonu)
      if (_cachedToken == token && _cachedUser != null && _isCacheValid()) {
        return _cachedUser;
      }

      // Token'ın geçerli olup olmadığını kontrol et
      if (!isTokenValid(token)) {
        _clearCache();
        return null;
      }

      // Token'ı decode et
      final payload = JwtDecoder.decode(token);

      // UserModel oluştur (Angular UserModel constructor'ına benzer)
      final user = UserModel.fromJwtPayload(payload);

      // Cache'e kaydet
      _cachedToken = token;
      _cachedUser = user;
      _cacheTime = DateTime.now();

      return user;
    } catch (e) {
      _clearCache();
      throw JwtException('Token decode edilemedi: $e');
    }
  }

  /// JWT token'ın geçerli olup olmadığını kontrol et - Cache'li
  bool isTokenValid(String? token) {
    if (token == null || token.isEmpty) return false;

    // Cache kontrolü (performans optimizasyonu)
    if (_cachedToken == token && _cachedTokenValid != null && _isCacheValid()) {
      return _cachedTokenValid!;
    }

    try {
      // Token'ın expire olup olmadığını kontrol et
      final isValid = !JwtDecoder.isExpired(token);

      // Cache'e kaydet
      _cachedToken = token;
      _cachedTokenValid = isValid;
      _cacheTime = DateTime.now();

      return isValid;
    } catch (e) {
      _clearCache();
      return false;
    }
  }

  /// JWT token'ın expire olup olmadığını kontrol et
  bool isTokenExpired(String? token) {
    if (token == null || token.isEmpty) return true;
    
    try {
      return JwtDecoder.isExpired(token);
    } catch (e) {
      return true;
    }
  }

  /// JWT token'ın expire olmasına ne kadar süre kaldığını hesapla
  Duration? getTokenRemainingTime(String? token) {
    if (token == null || token.isEmpty) return null;
    
    try {
      final payload = JwtDecoder.decode(token);
      final exp = payload['exp'] as int?;
      
      if (exp == null) return null;
      
      final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      final now = DateTime.now();
      
      if (expirationDate.isBefore(now)) return Duration.zero;
      
      return expirationDate.difference(now);
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın refresh edilmesi gerekip gerekmediğini kontrol et
  /// 15 dakikalık token için production optimize edildi - 2 dakika kala refresh et
  bool shouldRefreshToken(String? token) {
    if (token == null || token.isEmpty) return true;

    final remainingTime = getTokenRemainingTime(token);
    if (remainingTime == null) return true;

    final shouldRefresh = remainingTime <= AppConstants.tokenRefreshThreshold;

    // Production logging (daha az verbose)
    LoggingService.authLog('Token refresh check',
      details: 'Remaining: ${remainingTime.inMinutes}min, Should refresh: $shouldRefresh');

    return shouldRefresh;
  }

  /// Token'ın acil olarak refresh edilmesi gerekip gerekmediğini kontrol et
  /// (1 dakikadan az kaldıysa acil refresh gerekir)
  bool needsImmediateRefresh(String? token) {
    if (token == null || token.isEmpty) return true;

    final remainingTime = getTokenRemainingTime(token);
    if (remainingTime == null) return true;

    // 1 dakikadan az kaldıysa acil refresh gerekir
    return remainingTime <= AppConstants.immediateRefreshThreshold;
  }

  /// Token'ın kritik durumda olup olmadığını kontrol et
  /// (30 saniyeden az kaldıysa kritik durum)
  bool isTokenInCriticalState(String? token) {
    if (token == null || token.isEmpty) return true;

    final remainingTime = getTokenRemainingTime(token);
    if (remainingTime == null) return true;

    // 30 saniyeden az kaldıysa kritik durum
    return remainingTime <= AppConstants.criticalTokenThreshold;
  }

  /// Token'ın tamamen expire olup olmadığını kontrol et
  /// (15 saniyeden az kaldıysa expire sayılır)
  bool isTokenAboutToExpire(String? token) {
    if (token == null || token.isEmpty) return true;

    final remainingTime = getTokenRemainingTime(token);
    if (remainingTime == null) return true;

    // 15 saniyeden az kaldıysa expire sayılır
    return remainingTime <= const Duration(seconds: 15);
  }

  /// JWT token'dan belirli bir claim'i al
  T? getClaim<T>(String token, String claimName) {
    try {
      final payload = JwtDecoder.decode(token);
      return payload[claimName] as T?;
    } catch (e) {
      return null;
    }
  }

  /// JWT token'dan user ID'yi al
  int? getUserId(String token) {
    try {
      final nameIdentifier = getClaim<String>(
        token, 
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'
      );
      return int.tryParse(nameIdentifier ?? '');
    } catch (e) {
      return null;
    }
  }

  /// JWT token'dan email'i al
  String? getEmail(String token) {
    try {
      return getClaim<String>(token, '1'); // Backend'de email claim'i "1" key'i ile
    } catch (e) {
      return null;
    }
  }

  /// JWT token'dan name'i al
  String? getName(String token) {
    try {
      return getClaim<String>(
        token, 
        'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'
      );
    } catch (e) {
      return null;
    }
  }

  /// JWT token'dan role'ü al
  String? getRole(String token) {
    try {
      return getClaim<String>(
        token, 
        'http://schemas.microsoft.com/ws/2008/06/identity/claims/role'
      );
    } catch (e) {
      return null;
    }
  }

  /// JWT token'dan company ID'yi al
  int? getCompanyId(String token) {
    try {
      final companyIdStr = getClaim<String>(token, 'CompanyId');
      return int.tryParse(companyIdStr ?? '-1');
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın issuer'ını kontrol et
  bool isValidIssuer(String token, String expectedIssuer) {
    try {
      final issuer = getClaim<String>(token, 'iss');
      return issuer == expectedIssuer;
    } catch (e) {
      return false;
    }
  }

  /// JWT token'ın audience'ını kontrol et
  bool isValidAudience(String token, String expectedAudience) {
    try {
      final audience = getClaim<String>(token, 'aud');
      return audience == expectedAudience;
    } catch (e) {
      return false;
    }
  }

  /// JWT token'ın tam validation'ını yap
  bool validateToken(String token, {
    String? expectedIssuer,
    String? expectedAudience,
  }) {
    try {
      // Temel geçerlilik kontrolü
      if (!isTokenValid(token)) return false;
      
      // Issuer kontrolü (opsiyonel)
      if (expectedIssuer != null && !isValidIssuer(token, expectedIssuer)) {
        return false;
      }
      
      // Audience kontrolü (opsiyonel)
      if (expectedAudience != null && !isValidAudience(token, expectedAudience)) {
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// JWT token'dan tüm payload'ı al
  Map<String, dynamic>? getTokenPayload(String token) {
    try {
      return JwtDecoder.decode(token);
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın header'ını al
  Map<String, dynamic>? getTokenHeader(String token) {
    try {
      // JWT token'ı parçalara ayır
      final parts = token.split('.');
      if (parts.length != 3) return null;
      
      // Header'ı decode et
      final headerBase64 = parts[0];
      final headerJson = utf8.decode(base64Url.decode(headerBase64));
      return jsonDecode(headerJson) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın algoritmasını al
  String? getTokenAlgorithm(String token) {
    try {
      final header = getTokenHeader(token);
      return header?['alg'] as String?;
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın type'ını al
  String? getTokenType(String token) {
    try {
      final header = getTokenHeader(token);
      return header?['typ'] as String?;
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın issued at (iat) zamanını al
  DateTime? getTokenIssuedAt(String token) {
    try {
      final iat = getClaim<int>(token, 'iat');
      if (iat == null) return null;
      return DateTime.fromMillisecondsSinceEpoch(iat * 1000);
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın not before (nbf) zamanını al
  DateTime? getTokenNotBefore(String token) {
    try {
      final nbf = getClaim<int>(token, 'nbf');
      if (nbf == null) return null;
      return DateTime.fromMillisecondsSinceEpoch(nbf * 1000);
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ın expiration (exp) zamanını al
  DateTime? getTokenExpiration(String token) {
    try {
      final exp = getClaim<int>(token, 'exp');
      if (exp == null) return null;
      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      return null;
    }
  }

  /// JWT token'ı debug için string'e çevir
  String debugToken(String token) {
    try {
      final header = getTokenHeader(token);
      final payload = getTokenPayload(token);
      
      return '''
JWT Token Debug Info:
Header: ${jsonEncode(header)}
Payload: ${jsonEncode(payload)}
Is Valid: ${isTokenValid(token)}
Is Expired: ${isTokenExpired(token)}
Remaining Time: ${getTokenRemainingTime(token)}
Should Refresh: ${shouldRefreshToken(token)}
''';
    } catch (e) {
      return 'JWT Token Debug Error: $e';
    }
  }
}

/// JWT Exception
class JwtException implements Exception {
  final String message;
  
  const JwtException(this.message);
  
  @override
  String toString() => 'JwtException: $message';
}
