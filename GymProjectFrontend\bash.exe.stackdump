Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF15440000 ntdll.dll
7FFF13F20000 KERNEL32.DLL
7FFF12790000 KERNELBASE.dll
7FFF13910000 USER32.dll
7FFF13030000 win32u.dll
000210040000 msys-2.0.dll
7FFF147B0000 GDI32.dll
7FFF12590000 gdi32full.dll
7FFF12F80000 msvcp_win.dll
7FFF12E30000 ucrtbase.dll
7FFF13C90000 advapi32.dll
7FFF13570000 msvcrt.dll
7FFF15150000 sechost.dll
7FFF13760000 RPCRT4.dll
7FFF11CB0000 CRYPTBASE.DLL
7FFF12C10000 bcryptPrimitives.dll
7FFF13E60000 IMM32.DLL
