/// Token Debug Page - GymKod Pro Mobile
///
/// Bu sayfa token durumunu test etmek için kullanılır.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_spacing.dart';
import '../auth/presentation/providers/auth_provider.dart';

class TokenDebugPage extends ConsumerStatefulWidget {
  const TokenDebugPage({super.key});

  @override
  ConsumerState<TokenDebugPage> createState() => _TokenDebugPageState();
}

class _TokenDebugPageState extends ConsumerState<TokenDebugPage> {
  Map<String, dynamic>? debugInfo;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() {
      isLoading = true;
    });

    try {
      final authNotifier = ref.read(authProvider.notifier);
      final info = await authNotifier.getTokenDebugInfo();
      setState(() {
        debugInfo = info;
      });
    } catch (e) {
      setState(() {
        debugInfo = {'error': e.toString()};
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Token Debug'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Auth State Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Auth State',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    _buildInfoRow('Authenticated', authState.isAuthenticated.toString()),
                    _buildInfoRow('Initialized', authState.isInitialized.toString()),
                    _buildInfoRow('Loading', authState.isLoading.toString()),
                    _buildInfoRow('User', authState.user?.name ?? 'null'),
                    _buildInfoRow('Error', authState.error ?? 'null'),
                    _buildInfoRow('Require Password Change', authState.requirePasswordChange.toString()),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.md),

            // Token Debug Info Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Token Debug Info',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        IconButton(
                          onPressed: _loadDebugInfo,
                          icon: const Icon(Icons.refresh),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    if (isLoading)
                      const Center(child: CircularProgressIndicator())
                    else if (debugInfo != null)
                      ...debugInfo!.entries.map((entry) =>
                        _buildInfoRow(entry.key, entry.value.toString())
                      )
                    else
                      const Text('No debug info available'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: AppSpacing.md),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      final authNotifier = ref.read(authProvider.notifier);
                      await authNotifier.checkAuthStatus();
                      _loadDebugInfo();
                    },
                    child: const Text('Check Auth Status'),
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      final authNotifier = ref.read(authProvider.notifier);
                      await authNotifier.logout();
                      _loadDebugInfo();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: AppColors.onError,
                    ),
                    child: const Text('Logout'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppSpacing.md),

            // Token Test Buttons
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      final authNotifier = ref.read(authProvider.notifier);
                      final result = await authNotifier.validateAndRefreshTokens();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(result ? 'Token validation successful' : 'Token validation failed'),
                            backgroundColor: result ? AppColors.success : AppColors.error,
                          ),
                        );
                      }
                      _loadDebugInfo();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.info,
                      foregroundColor: AppColors.white,
                    ),
                    child: const Text('Test Token Validation'),
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      final authNotifier = ref.read(authProvider.notifier);
                      final result = await authNotifier.triggerManualTokenRefresh();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(result ? 'Manual token refresh successful' : 'Manual token refresh failed'),
                            backgroundColor: result ? AppColors.success : AppColors.error,
                          ),
                        );
                      }
                      _loadDebugInfo();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.warning,
                      foregroundColor: AppColors.black,
                    ),
                    child: const Text('Manual Token Refresh'),
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      final authNotifier = ref.read(authProvider.notifier);
                      await authNotifier.triggerManualBackgroundCheck();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Manual background check triggered'),
                            backgroundColor: AppColors.info,
                          ),
                        );
                      }
                      _loadDebugInfo();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      foregroundColor: AppColors.white,
                    ),
                    child: const Text('Manual Background Check'),
                  ),
                ),
                const SizedBox(height: AppSpacing.sm),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // Test member-main navigation
                      context.go('/member-main');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.white,
                    ),
                    child: const Text('Test Member Main Navigation'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: value == 'true' ? AppColors.success : 
                       value == 'false' ? AppColors.error : 
                       value == 'null' ? AppColors.onSurfaceVariant : 
                       AppColors.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
