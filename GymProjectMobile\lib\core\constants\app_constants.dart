/// App Constants - GymKod Pro Mobile
///
/// Bu dosya Angular frontend'deki tasarım sisteminden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/styles.css ve modern-components.css
library;

class AppConstants {
  // API Configuration
  // Mobil cihazlar için bilgisayarın IP adresini kullan (HTTP - HTTPS sertifika sorunu için)
  // NOT: Bu IP adresini kendi bilgisayarınızın IP adresi ile değiştirin
  static const String baseUrl = 'http://*************:5165/api/';
  static const String apiVersion = 'v1';

  // JWT Token Configuration
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';

  // Token Refresh Configuration - Production Ready for 15-minute token lifespan
  // Backend AccessTokenExpiration: 15 dakika için optimize edildi

  // Token süresi dolmadan 2 dakika önce yenile (15 dakikalık token için)
  static const Duration tokenRefreshThreshold = Duration(minutes: 2);

  // Token geçerliliğini her 5 dakikada bir kontrol et (10K+ kullanıcı için optimize edildi)
  static const Duration tokenValidityCheckInterval = Duration(minutes: 5);

  // Background'da token kontrolü için interval (10K+ kullanıcı için optimize edildi)
  static const Duration backgroundTokenCheckInterval = Duration(minutes: 5);

  // App resume'da token kontrolü için minimum süre (production optimized)
  static const Duration appResumeTokenCheckThreshold = Duration(seconds: 30);

  // Token refresh retry configuration (production optimized)
  static const int maxTokenRefreshRetries = 3;
  static const Duration tokenRefreshRetryDelay = Duration(seconds: 5);

  // Acil token refresh threshold'ları (15 dakikalık token için)
  static const Duration immediateRefreshThreshold = Duration(minutes: 1); // 1 dakika kala acil refresh
  static const Duration criticalTokenThreshold = Duration(seconds: 30); // 30 saniye kala kritik durum

  // App Information
  static const String appName = 'GymKod Pro';
  static const String appDescription = 'Profesyonel Spor Salonu Yönetim Sistemi';
  static const String appVersion = '1.0.0';

  // Animation Durations (Angular frontend'deki transition-speed'den)
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);

  // QR Code Configuration
  static const Duration qrCodeValidityDuration = Duration(minutes: 5);
  static const String qrCodeEncryptionKey = 'GymKodProQREncryption2024';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Validation
  static const int minPasswordLength = 4;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // Device Info
  static const String deviceTypeKey = 'device_type';
  static const String deviceIdKey = 'device_id';
  static const String deviceNameKey = 'device_name';

  // Error Messages
  static const String networkErrorMessage = 'İnternet bağlantınızı kontrol edin';
  static const String serverErrorMessage = 'Sunucu hatası oluştu';
  static const String unknownErrorMessage = 'Bilinmeyen bir hata oluştu';
  static const String tokenExpiredMessage = 'Oturum süreniz doldu, lütfen tekrar giriş yapın';

  // Success Messages
  static const String loginSuccessMessage = 'Giriş başarılı';
  static const String logoutSuccessMessage = 'Çıkış başarılı';
  static const String registerSuccessMessage = 'Kayıt başarılı';

  // Form Validation Messages
  static const String emailRequiredMessage = 'E-posta adresi gerekli';
  static const String emailInvalidMessage = 'Geçerli bir e-posta adresi girin';
  static const String passwordRequiredMessage = 'Şifre gerekli';
  static const String passwordTooShortMessage = 'Şifre en az $minPasswordLength karakter olmalı';
  static const String nameRequiredMessage = 'Ad gerekli';
  static const String phoneRequiredMessage = 'Telefon numarası gerekli';
  static const String phoneInvalidMessage = 'Geçerli bir telefon numarası girin';

  // Storage Keys
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String firstLaunchKey = 'first_launch';
  static const String biometricEnabledKey = 'biometric_enabled';

  // Theme Mode Values
  static const String lightTheme = 'light';
  static const String darkTheme = 'dark';
  static const String systemTheme = 'system';

  // Language Codes
  static const String turkishLanguage = 'tr';
  static const String englishLanguage = 'en';

  // Regex Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^[0-9]{10,11}$';
  static const String passwordPattern = r'^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$';

  // HTTP Status Codes
  static const int httpOk = 200;
  static const int httpCreated = 201;
  static const int httpBadRequest = 400;
  static const int httpUnauthorized = 401;
  static const int httpForbidden = 403;
  static const int httpNotFound = 404;
  static const int httpInternalServerError = 500;

  // Cache Duration
  static const Duration shortCacheDuration = Duration(minutes: 5);
  static const Duration mediumCacheDuration = Duration(minutes: 30);
  static const Duration longCacheDuration = Duration(hours: 24);
}
