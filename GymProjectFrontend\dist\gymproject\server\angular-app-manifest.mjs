
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41452, hash: 'f43efcd51c554835d6dae3e6654535f43c57e75c8e28396fb28f8ab3664753da', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '8ce36de908f4f772a73f81aff909fa9ddff26a95441892bdf6bdd1e79312dbfb', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-MC4B3AGM.css': {size: 294869, hash: '1A4AQVWAvyE', text: () => import('./assets-chunks/styles-MC4B3AGM_css.mjs').then(m => m.default)}
  },
};
