Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8790) msys-2.0.dll+0x1FE8E
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210286019, 0007FFFF9748, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9890  000210068E24 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9B70  00021006A225 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF59F00000 ntdll.dll
7FFF58390000 KERNEL32.DLL
7FFF57570000 KERNELBASE.dll
7FFF59690000 USER32.dll
7FFF57480000 win32u.dll
7FFF59660000 GDI32.dll
7FFF572A0000 gdi32full.dll
7FFF57AC0000 msvcp_win.dll
7FFF57B70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF59420000 advapi32.dll
7FFF59880000 msvcrt.dll
7FFF59560000 sechost.dll
7FFF58010000 RPCRT4.dll
7FFF567A0000 CRYPTBASE.DLL
7FFF573E0000 bcryptPrimitives.dll
7FFF57D90000 IMM32.DLL
